/// 바라 부스 매니저 - 구글 드라이브 백업 화면
///
/// 구글 드라이브를 통한 데이터 백업 및 복원 기능을 제공하는 화면입니다.
/// - 구글 계정 연동
/// - 데이터 백업
/// - 백업 파일 목록 조회
/// - 데이터 복원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../services/google_drive_service.dart';
import '../../services/backup_service.dart';
import '../../services/database_service.dart';
import '../../providers/subscription_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/app_bar_styles.dart';

/// 구글 드라이브 백업 화면
class GoogleDriveBackupScreen extends ConsumerStatefulWidget {
  const GoogleDriveBackupScreen({super.key});

  @override
  ConsumerState<GoogleDriveBackupScreen> createState() => _GoogleDriveBackupScreenState();
}

class _GoogleDriveBackupScreenState extends ConsumerState<GoogleDriveBackupScreen> {
  static const String _tag = 'GoogleDriveBackupScreen';

  final GoogleDriveService _driveService = GoogleDriveService();
  late final BackupService _backupService;

  bool _isLoading = false;
  bool _isAuthenticated = false;
  GoogleSignInAccount? _currentAccount;
  List<Map<String, dynamic>> _backupFiles = [];

  @override
  void initState() {
    super.initState();
    _backupService = BackupService(DatabaseServiceImpl());
    _checkAuthStatus();
  }

  /// 인증 상태 확인
  Future<void> _checkAuthStatus() async {
    setState(() => _isLoading = true);
    
    try {
      // 저장된 인증 정보 확인
      final authInfo = await _driveService.getStoredAuthInfo();
      if (authInfo != null) {
        _isAuthenticated = true;
        // 현재 계정 정보 가져오기
        _currentAccount = await _driveService.currentAccount;
      }

      if (_isAuthenticated) {
        await _loadBackupFiles();
      }
    } catch (e) {
      LoggerUtils.logError('인증 상태 확인 실패', tag: _tag, error: e);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 구글 계정 연동
  Future<void> _authenticateGoogle() async {
    // 플러스 플랜 확인
    final isPlusUser = await ref.read(isPlusUserProvider.future);
    if (!isPlusUser) {
      _showUpgradeDialog();
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await _driveService.authenticate();
      if (success) {
        _isAuthenticated = true;
        _currentAccount = await _driveService.currentAccount;
        await _loadBackupFiles();
        
        if (mounted) {
          ToastUtils.showSuccess(context, '구글 계정 연동이 완료되었습니다.');
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, '구글 계정 연동에 실패했습니다.');
        }
      }
    } catch (e) {
      LoggerUtils.logError('구글 인증 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '구글 계정 연동 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 백업 파일 목록 로드
  Future<void> _loadBackupFiles() async {
    try {
      final files = await _driveService.getBackupFiles();
      setState(() => _backupFiles = files);
    } catch (e) {
      LoggerUtils.logError('백업 파일 목록 로드 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '백업 파일 목록을 불러올 수 없습니다.');
      }
    }
  }

  /// 데이터 백업
  Future<void> _backupData() async {
    setState(() => _isLoading = true);

    try {
      // 백업 데이터 생성
      final backupData = await _backupService.createBackup();
      
      // 구글 드라이브에 업로드
      final success = await _driveService.backupData(backupData.toJson());
      
      if (success) {
        await _loadBackupFiles(); // 목록 새로고침
        if (mounted) {
          ToastUtils.showSuccess(context, '데이터 백업이 완료되었습니다.');
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, '데이터 백업에 실패했습니다.');
        }
      }
    } catch (e) {
      LoggerUtils.logError('데이터 백업 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '백업 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 데이터 복원
  Future<void> _restoreData(String fileId, String fileName) async {
    final confirmed = await _showRestoreConfirmDialog(fileName);
    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      // 구글 드라이브에서 데이터 다운로드
      final data = await _driveService.restoreData(fileId);
      if (data == null) {
        throw Exception('백업 파일을 읽을 수 없습니다');
      }

      // 백업 데이터 복원
      final backupData = BackupData.fromJson(data);
      final success = await _backupService.restoreBackup(backupData);

      if (success) {
        if (mounted) {
          ToastUtils.showSuccess(context, '데이터 복원이 완료되었습니다.\n앱을 재시작해주세요.');
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, '데이터 복원에 실패했습니다.');
        }
      }
    } catch (e) {
      LoggerUtils.logError('데이터 복원 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '복원 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 백업 파일 삭제
  Future<void> _deleteBackupFile(String fileId, String fileName) async {
    final confirmed = await _showDeleteConfirmDialog(fileName);
    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      final success = await _driveService.deleteBackupFile(fileId);
      if (success) {
        await _loadBackupFiles(); // 목록 새로고침
        if (mounted) {
          ToastUtils.showSuccess(context, '백업 파일이 삭제되었습니다.');
        }
      } else {
        if (mounted) {
          ToastUtils.showError(context, '백업 파일 삭제에 실패했습니다.');
        }
      }
    } catch (e) {
      LoggerUtils.logError('백업 파일 삭제 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '삭제 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 구글 계정 연동 해제
  Future<void> _signOut() async {
    final confirmed = await _showSignOutConfirmDialog();
    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      await _driveService.signOut();
      setState(() {
        _isAuthenticated = false;
        _currentAccount = null;
        _backupFiles.clear();
      });
      
      if (mounted) {
        ToastUtils.showSuccess(context, '구글 계정 연동이 해제되었습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('구글 계정 연동 해제 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '연동 해제 중 오류가 발생했습니다.');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('구글 드라이브로 백업', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 24),
                    _buildAuthSection(),
                    if (_isAuthenticated) ...[
                      const SizedBox(height: 24),
                      _buildBackupSection(),
                      const SizedBox(height: 24),
                      _buildBackupFilesList(),
                    ],
                  ],
                ),
              ),
      ),
    );
  }

  /// 정보 카드
  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primarySeed),
                const SizedBox(width: 8),
                Text(
                  '구글 드라이브 백업',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primarySeed,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 모든 행사, 상품, 판매 데이터를 안전하게 백업합니다\n'
              '• 백업 파일은 본인의 구글 드라이브에 저장됩니다\n'
              '• 언제든지 데이터를 복원할 수 있습니다\n'
              '• 플러스 플랜 전용 기능입니다',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 인증 섹션
  Widget _buildAuthSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '구글 계정 연동',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_isAuthenticated && _currentAccount != null) ...[
              ListTile(
                leading: CircleAvatar(
                  backgroundImage: _currentAccount!.photoUrl != null
                      ? NetworkImage(_currentAccount!.photoUrl!)
                      : null,
                  child: _currentAccount!.photoUrl == null
                      ? Icon(Icons.person)
                      : null,
                ),
                title: Text(_currentAccount!.displayName ?? ''),
                subtitle: Text(_currentAccount!.email),
                trailing: TextButton(
                  onPressed: _signOut,
                  child: Text('연동 해제'),
                ),
              ),
            ] else ...[
              const Text(
                '구글 계정을 연동하여 백업 기능을 사용하세요.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _authenticateGoogle,
                  icon: Icon(Icons.login),
                  label: Text('구글 계정 연동하기'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 백업 섹션
  Widget _buildBackupSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '데이터 백업',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '현재 앱의 모든 데이터를 구글 드라이브에 백업합니다.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _backupData,
                icon: Icon(Icons.backup),
                label: Text('지금 백업하기'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 백업 파일 목록
  Widget _buildBackupFilesList() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '백업 파일 목록',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: _loadBackupFiles,
                  icon: Icon(Icons.refresh, size: 16),
                  label: Text('새로고침'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_backupFiles.isEmpty) ...[
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    '백업 파일이 없습니다.\n위의 "지금 백업하기" 버튼을 눌러 백업을 생성하세요.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ] else ...[
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _backupFiles.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final file = _backupFiles[index];
                  return _buildBackupFileItem(file);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 백업 파일 아이템
  Widget _buildBackupFileItem(Map<String, dynamic> file) {
    final fileName = file['name'] as String;
    final createdTime = file['createdTime'] as String?;
    final size = file['size'] as String?;
    final fileId = file['id'] as String;

    DateTime? createdDate;
    if (createdTime != null) {
      try {
        createdDate = DateTime.parse(createdTime);
      } catch (e) {
        // 파싱 실패 시 무시
      }
    }

    return ListTile(
      leading: Icon(Icons.folder, color: AppColors.primarySeed),
      title: Text(
        fileName,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (createdDate != null)
            Text(
              '생성일: ${createdDate.year}-${createdDate.month.toString().padLeft(2, '0')}-${createdDate.day.toString().padLeft(2, '0')} '
              '${createdDate.hour.toString().padLeft(2, '0')}:${createdDate.minute.toString().padLeft(2, '0')}',
              style: const TextStyle(fontSize: 12),
            ),
          if (size != null)
            Text(
              '크기: ${_formatFileSize(int.tryParse(size) ?? 0)}',
              style: const TextStyle(fontSize: 12),
            ),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (value) {
          switch (value) {
            case 'restore':
              _restoreData(fileId, fileName);
              break;
            case 'delete':
              _deleteBackupFile(fileId, fileName);
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'restore',
            child: Row(
              children: [
                Icon(Icons.restore, size: 16),
                SizedBox(width: 8),
                Text('복원하기'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red),
                SizedBox(width: 8),
                Text('삭제하기', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 파일 크기 포맷팅
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 플러스 플랜 업그레이드 다이얼로그
  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('플러스 플랜 전용 기능'),
        content: const Text(
          '구글 드라이브 백업은 플러스 플랜 전용 기능입니다.\n'
          '플러스 플랜으로 업그레이드하시겠습니까?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 구독 관리 페이지로 이동
              Navigator.of(context).pushNamed('/subscription');
            },
            child: const Text('업그레이드'),
          ),
        ],
      ),
    );
  }

  /// 데이터 복원 확인 다이얼로그
  Future<bool> _showRestoreConfirmDialog(String fileName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('데이터 복원'),
        content: Text(
          '백업 파일 "$fileName"에서 데이터를 복원하시겠습니까?\n\n'
          '⚠️ 현재 데이터가 백업 데이터로 덮어씌워집니다.\n'
          '복원 후에는 앱을 재시작해야 합니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('복원하기'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 백업 파일 삭제 확인 다이얼로그
  Future<bool> _showDeleteConfirmDialog(String fileName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('백업 파일 삭제'),
        content: Text(
          '백업 파일 "$fileName"을 삭제하시겠습니까?\n\n'
          '⚠️ 삭제된 백업 파일은 복구할 수 없습니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('삭제하기'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 구글 계정 연동 해제 확인 다이얼로그
  Future<bool> _showSignOutConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('구글 계정 연동 해제'),
        content: const Text(
          '구글 계정 연동을 해제하시겠습니까?\n\n'
          '연동을 해제하면 백업 기능을 사용할 수 없습니다.\n'
          '기존 백업 파일은 구글 드라이브에 그대로 남아있습니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('연동 해제'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
