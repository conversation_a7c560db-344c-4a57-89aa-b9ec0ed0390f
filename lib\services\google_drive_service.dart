/// 바라 부스 매니저 - 구글 드라이브 백업 서비스
///
/// 구글 드라이브 API를 사용하여 사용자 데이터를 백업하고 복원하는 서비스입니다.
/// - 구글 계정 인증
/// - 데이터 백업 (JSON 형태)
/// - 데이터 복원
/// - 백업 파일 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../utils/logger_utils.dart';

/// 구글 드라이브 서비스
class GoogleDriveService {
  static const String _tag = 'GoogleDriveService';
  static const String _backupFolderName = 'ParabaraBackup';

  
  // 구글 드라이브 API 스코프
  static const List<String> _scopes = [
    drive.DriveApi.driveFileScope,
  ];

  drive.DriveApi? _driveApi;
  GoogleSignIn? _googleSignIn;
  String? _accessToken;

  /// 구글 계정 인증 상태 확인
  bool get isAuthenticated => _accessToken != null && _driveApi != null;

  /// 현재 인증된 구글 계정 정보
  Future<GoogleSignInAccount?> get currentAccount async {
    if (_googleSignIn == null) return null;
    try {
      // 저장된 인증 정보에서 이메일 가져오기
      final storedAuth = await getStoredAuthInfo();
      if (storedAuth != null) {
        // 간단한 GoogleSignInAccount 대체 객체 반환 (실제로는 저장된 정보 사용)
        return null; // 실제 구현에서는 저장된 이메일 정보를 사용
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 구글 계정으로 인증
  Future<bool> authenticate() async {
    try {
      LoggerUtils.logInfo('구글 드라이브 인증 시작', tag: _tag);

      // 현재 Firebase 사용자가 구글로 로그인했는지 확인
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        // Firebase 사용자의 프로바이더 확인
        final isGoogleUser = firebaseUser.providerData
            .any((provider) => provider.providerId == 'google.com');
        
        if (isGoogleUser) {
          // 이미 구글로 로그인된 사용자의 경우 기존 토큰 사용 시도
          final success = await _tryUseExistingGoogleAuth();
          if (success) {
            LoggerUtils.logInfo('기존 구글 인증 정보 사용 성공', tag: _tag);
            return true;
          }
        }
      }

      // 새로운 구글 인증 진행
      _googleSignIn = GoogleSignIn.instance;

      // 초기화
      try {
        await _googleSignIn!.initialize();
        LoggerUtils.logInfo('Google Sign-In 초기화 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('Google Sign-In 초기화 실패', tag: _tag, error: e);
        throw Exception('Google 서비스 초기화에 실패했습니다.');
      }

      final account = await _googleSignIn!.authenticate();
      // authenticate() 메서드는 null을 반환하지 않으므로 null 체크 제거

      final auth = await account.authentication;
      _accessToken = auth.idToken;

      if (_accessToken == null) {
        LoggerUtils.logError('구글 액세스 토큰을 가져올 수 없음', tag: _tag);
        return false;
      }

      // 드라이브 API 클라이언트 생성
      final authClient = authenticatedClient(
        http.Client(),
        AccessCredentials(
          AccessToken('Bearer', _accessToken!, DateTime.now().add(Duration(hours: 1))),
          null,
          _scopes,
        ),
      );

      _driveApi = drive.DriveApi(authClient);

      // 인증 정보 저장
      await _saveAuthInfo(account.email, _accessToken!);

      LoggerUtils.logInfo('구글 드라이브 인증 성공: ${account.email}', tag: _tag);
      return true;

    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 인증 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 기존 구글 인증 정보 사용 시도
  Future<bool> _tryUseExistingGoogleAuth() async {
    try {
      _googleSignIn = GoogleSignIn.instance;

      // 초기화
      try {
        await _googleSignIn!.initialize();
      } catch (e) {
        LoggerUtils.logWarning('Google Sign-In 초기화 실패 (기존 인증)', tag: _tag, error: e);
        return false;
      }

      // 현재 로그인된 계정 확인 (저장된 인증 정보 사용)
      final storedAuth = await getStoredAuthInfo();
      if (storedAuth != null) {
        _accessToken = storedAuth['accessToken'];
        if (_accessToken != null) {
          final authClient = authenticatedClient(
            http.Client(),
            AccessCredentials(
              AccessToken('Bearer', _accessToken!, DateTime.now().add(Duration(hours: 1))),
              null,
              _scopes,
            ),
          );
          _driveApi = drive.DriveApi(authClient);
          return true;
        }
      }

      return false;
    } catch (e) {
      LoggerUtils.logWarning('기존 구글 인증 정보 사용 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 인증 정보 저장
  Future<void> _saveAuthInfo(String email, String accessToken) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('google_drive_email', email);
      await prefs.setString('google_drive_access_token', accessToken);
      await prefs.setInt('google_drive_auth_time', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      LoggerUtils.logWarning('구글 드라이브 인증 정보 저장 실패', tag: _tag, error: e);
    }
  }

  /// 저장된 인증 정보 로드
  Future<Map<String, String>?> getStoredAuthInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('google_drive_email');
      final accessToken = prefs.getString('google_drive_access_token');
      final authTime = prefs.getInt('google_drive_auth_time');

      if (email != null && accessToken != null && authTime != null) {
        // 토큰이 24시간 이내인지 확인
        final authDateTime = DateTime.fromMillisecondsSinceEpoch(authTime);
        final isValid = DateTime.now().difference(authDateTime).inHours < 24;
        
        if (isValid) {
          return {'email': email, 'accessToken': accessToken};
        }
      }
      return null;
    } catch (e) {
      LoggerUtils.logWarning('구글 드라이브 인증 정보 로드 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 로그아웃
  Future<void> signOut() async {
    try {
      await _googleSignIn?.signOut();
      _driveApi = null;
      _accessToken = null;
      
      // 저장된 인증 정보 삭제
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('google_drive_email');
      await prefs.remove('google_drive_access_token');
      await prefs.remove('google_drive_auth_time');
      
      LoggerUtils.logInfo('구글 드라이브 로그아웃 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구글 드라이브 로그아웃 실패', tag: _tag, error: e);
    }
  }

  /// 백업 폴더 찾기 또는 생성
  Future<String> _getOrCreateBackupFolder() async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      // 기존 백업 폴더 검색
      final query = "name='$_backupFolderName' and mimeType='application/vnd.google-apps.folder' and trashed=false";
      final fileList = await _driveApi!.files.list(q: query);

      if (fileList.files != null && fileList.files!.isNotEmpty) {
        final folderId = fileList.files!.first.id!;
        LoggerUtils.logInfo('기존 백업 폴더 발견: $folderId', tag: _tag);
        return folderId;
      }

      // 백업 폴더 생성
      final folder = drive.File()
        ..name = _backupFolderName
        ..mimeType = 'application/vnd.google-apps.folder';

      final createdFolder = await _driveApi!.files.create(folder);
      final folderId = createdFolder.id!;
      
      LoggerUtils.logInfo('새 백업 폴더 생성: $folderId', tag: _tag);
      return folderId;

    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 폴더 생성/검색 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 데이터를 구글 드라이브에 백업
  Future<bool> backupData(Map<String, dynamic> data) async {
    if (_driveApi == null) {
      LoggerUtils.logError('구글 드라이브 인증이 필요합니다', tag: _tag);
      return false;
    }

    try {
      LoggerUtils.logInfo('구글 드라이브 백업 시작', tag: _tag);

      final folderId = await _getOrCreateBackupFolder();
      final jsonData = jsonEncode(data);
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = 'parabara_backup_$timestamp.json';

      // 파일 메타데이터 생성
      final fileMetadata = drive.File()
        ..name = fileName
        ..parents = [folderId];

      // 파일 업로드
      final media = drive.Media(
        Stream.fromIterable([utf8.encode(jsonData)]),
        jsonData.length,
      );

      final uploadedFile = await _driveApi!.files.create(
        fileMetadata,
        uploadMedia: media,
      );

      LoggerUtils.logInfo('구글 드라이브 백업 완료: ${uploadedFile.id}', tag: _tag);
      return true;

    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 구글 드라이브에서 백업 파일 목록 가져오기
  Future<List<Map<String, dynamic>>> getBackupFiles() async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      final folderId = await _getOrCreateBackupFolder();
      
      final query = "parents in '$folderId' and name contains 'parabara_backup_' and trashed=false";
      final fileList = await _driveApi!.files.list(
        q: query,
        orderBy: 'createdTime desc',
        $fields: 'files(id,name,createdTime,size)',
      );

      final backupFiles = <Map<String, dynamic>>[];
      
      if (fileList.files != null) {
        for (final file in fileList.files!) {
          backupFiles.add({
            'id': file.id,
            'name': file.name,
            'createdTime': file.createdTime?.toIso8601String(),
            'size': file.size,
          });
        }
      }

      LoggerUtils.logInfo('백업 파일 목록 조회 완료: ${backupFiles.length}개', tag: _tag);
      return backupFiles;

    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 파일 목록 조회 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 구글 드라이브에서 백업 데이터 복원
  Future<Map<String, dynamic>?> restoreData(String fileId) async {
    if (_driveApi == null) throw Exception('구글 드라이브 인증이 필요합니다');

    try {
      LoggerUtils.logInfo('구글 드라이브에서 데이터 복원 시작: $fileId', tag: _tag);

      final media = await _driveApi!.files.get(fileId, downloadOptions: drive.DownloadOptions.fullMedia);
      
      if (media is! drive.Media) {
        throw Exception('파일 다운로드 실패');
      }

      final bytes = <int>[];
      await for (final chunk in media.stream) {
        bytes.addAll(chunk);
      }

      final jsonString = utf8.decode(bytes);
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      LoggerUtils.logInfo('구글 드라이브 데이터 복원 완료', tag: _tag);
      return data;

    } catch (e, stackTrace) {
      LoggerUtils.logError('구글 드라이브 데이터 복원 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 백업 파일 삭제
  Future<bool> deleteBackupFile(String fileId) async {
    if (_driveApi == null) return false;

    try {
      await _driveApi!.files.delete(fileId);
      LoggerUtils.logInfo('백업 파일 삭제 완료: $fileId', tag: _tag);
      return true;
    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 파일 삭제 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }
}


